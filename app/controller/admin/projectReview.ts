/**
 * @file 项目审核
 * <AUTHOR>
 */

'use strict';

import { Controller } from 'egg';
import * as J<PERSON>Z<PERSON> from 'jszip';

import baseError from '../../core/base/baseError';
import validate from '../../core/decorators/validate';
import { cleanJsonNodes } from '../../core/utils/htmlToJsonV4';
import { Op } from 'sequelize';

export default class ProjectReviewController extends Controller {

  @validate({ projectId: 'number' })
  public async apply() {
    const {
      service,
      ctx,
    } = this;
    const { projectId } = ctx.input;
    const { statuses } = service.project.base;
    const { userId } = ctx.data;
    const project = await service.project.base.getOne({
      where: { id: projectId },
      attributes: ['status', 'appKey', 'startReviewTime'],
    });
    if (!project || project.status !== statuses.unreviewed) {
      return ctx.body = baseError.dataNotExistError('项目不存在或者处于不可审核的状态');
    }

    // 构建更新数据，只有当startReviewTime为空时才设置
    const updateData: any = {
      reviewUserId: userId,
      status: statuses.reviewing,
    };

    if (!project.startReviewTime) {
      updateData.startReviewTime = new Date();
    }

    await service.project.base.update(
      updateData,
      { where: { id: projectId } }
    );
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.reviewTypes.apply,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ projectId: 'number' })
  public async quit() {
    const {
      service,
      ctx,
    } = this;
    const { projectId } = ctx.input;
    const { statuses } = service.project.base;
    const { userId } = ctx.data;
    const project = await service.project.base.getOne({
      where: { id: projectId },
      attributes: ['status', 'appKey', 'reviewUserId'],
    });
    if (!project || project.status !== statuses.reviewing) {
      return ctx.body = baseError.dataNotExistError('项目不存在或者处于不可审核的状态');
    }
    if (project.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人审核，无须放弃');
    }
    await service.project.base.update(
      {
        reviewUserId: 0,
        status: statuses.unreviewed,
        startReviewTime: null,
      },
      { where: { id: projectId } }
    );
    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.reviewTypes.quit,
        data: '',
        costTime: 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ projectId: 'number' })
  public async applyWordsProject() {
    const {
      service,
      ctx,
      logger,
    } = this;
    const {
      projectId,
      costTime,
    } = ctx.input as { projectId: number; costTime?: number; };
    const { statuses } = service.project.base;
    const { types } = service.book;
    const { userId } = ctx.data;
    const [project, books] = await Promise.all([
      service.project.base.getOne({ where: { id: projectId } }),
      service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      })
    ]);
    const book = books?.find((_book) => _book.type === types.question);
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (!book) {
      return ctx.body = baseError.dataNotExistError('试题图书不存在');
    }
    const allTasks = await service.task.base.getAll({
      where: { bookId: books.map((_book) => _book.id) },
      attributes: ['taskId', 'appKey', 'bookId', 'taskName', 'status'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });

    // 构建更新数据，只有当endReviewTime为空时才设置
    const updateData: any = { status: statuses.publishing };

    if (!project.endReviewTime) {
      updateData.endReviewTime = new Date();
    }

    await service.project.base.update(
      updateData,
      { where: { id: projectId } }
    );

    logger.info(`[applyWordsProject] begin projectId: ${projectId} tasks:${JSON.stringify(allTasks)}`);

    ctx.runInBackground(async() => {
      await this.service.project.base.pushToApplyProject({
        projectId,
        userId,
        costTime,
      });
    });

    ctx.body = { status: 0 };
  }

  @validate({
    projectId: 'number',
    costTime: {
      type: 'number',
      required: false,
    },
  })
  public async confirm() {
    const {
      service,
      ctx,
      logger,
    } = this;
    const {
      projectId,
      costTime,
    } = ctx.input as { projectId: number; costTime?: number; };
    const { statuses } = service.project.base;
    const { statuses: taskStatuses } = service.task.base;
    const { types } = service.book;
    const { userId } = ctx.data;
    const [project, books] = await Promise.all([
      service.project.base.getOne({ where: { id: projectId } }),
      service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      })
    ]);
    if (!project || project.status !== statuses.reviewing) {
      return ctx.body = baseError.dataNotExistError('项目不存在或者处于不可审核的状态');
    }
    const book = books.find((_book) => _book.type === types.question);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('试题图书不存在');
    }
    const allTasks = await service.task.base.getAll({
      where: {
        bookId: books.map((_book) => _book.id),
        mergedTaskId: null,
      },
      attributes: ['taskId', 'appKey', 'bookId', 'taskName', 'status'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    const questionTasks = allTasks.filter((task) => task.bookId === book.id);
    if (!questionTasks.length || questionTasks.some((task) => task.status !== taskStatuses.reviewed)) {
      return ctx.body = baseError.dataAlreadyExistError('不存在任务或者部分任务不是已发布状态');
    }
    if (project.reviewUserId !== userId) {
      return ctx.body = baseError.dataNotExistError('非本人审核，无权限提交');
    }

    // 构建更新数据，只有当endReviewTime为空时才设置
    const updateData: any = { status: statuses.publishing };

    if (!project.endReviewTime) {
      updateData.endReviewTime = new Date();
    }

    await service.project.base.update(
      updateData,
      { where: { id: projectId } }
    );
    // 是否需要在项目发布时自动转换 word 并交付
    const resWord = project.isResWord;
    if (resWord) {
      const htmls = await Promise.all(allTasks.map(async(_task) => {
        const html: string = await service.task.base.getOssData(_task.appKey, _task.taskId, 'formatted.html');
        return html;
      }));
      const html = htmls.join('\n');
      // 上传
      const { data } = await service.rbs.initRBSQueue({
        task_id: project.id.toString(),
        task_type: 'html_docx',
        task_info: {
          projectId: project.id,
          jobName: 'generateDocxFromHtml',
          isResWord: true,
          queue: true,
          params: {
            html: html,
            file_name: project.projectName + '.docx',
            config: { disable_choice_layout: false },
            taskId: project.id,
            output: 'url',
          },
          callback: {
            // url: 'http://qnsizsyi51t6.ngrok.xiaomiqiu123.top/api/open/project/word/callback',
            url: 'http://xdoc.open.hexinedu.com/api/open/project/word/callback',
            method: 'POST',
            returnProps: ['projectId', 'isResWord', 'status', 'result', 'reason'],
            successStatus: {
              'prop': 'status',
              'value': [0],
            },
          },
          push_time: new Date().getTime(),
          timestamp: new Date().getTime(),
        },
      });
      this.logger.info('html2word', data);
    } else {
      // Push 到项目发布队列中
      await service.project.base.pushToPublish(projectId);
      logger.info(`[PublishProject] push into queue, project_id=${projectId}`);
    }

    // 记录操作日志
    ctx.runInBackground(async() => {
      await service.project.history.create({
        projectId,
        userId,
        type: service.project.history.reviewTypes.confirm,
        data: '',
        costTime: costTime || 0,
      });
    });
    ctx.body = { status: 0 };
  }

  @validate({ projectId: 'number' })
  public async reMergeProjectJson() {
    const {
      service,
      ctx,
    } = this;
    const { projectId } = ctx.input;
    const { statuses } = service.project.base;
    const { statuses: taskStatuses } = service.task.base;
    const { types } = service.book;
    const { userId } = ctx.data;
    const [project, books] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['status', 'appKey', 'reviewUserId'],
      }),
      service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      })
    ]);
    if (!project || project.status !== statuses.reviewed) {
      return ctx.body = baseError.dataNotExistError('项目不存在或者处于不能操作的状态');
    }
    const book = books.find((book) => book.type === types.question);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('试题图书不存在');
    }
    const allTasks = await service.task.base.getAll({
      where: { bookId: books.map((book) => book.id) },
      attributes: ['taskId', 'appKey', 'bookId', 'taskName', 'status'],
      order: [['bookOrder', 'ASC'], ['id', 'ASC']],
    });
    const questionTasks = allTasks.filter((task) => task.bookId === book.id);
    if (!questionTasks.length || questionTasks.some((task) => task.status !== taskStatuses.reviewed)) {
      return ctx.body = baseError.dataAlreadyExistError('不存在任务或者部分任务不是已发布状态');
    }
    const isSpecial = await service.themis.hasRoleByUserId(userId, ({ name }) => name === '管理员');
    if (!isSpecial) {
      return ctx.body = baseError.dataNotExistError('非管理员，无权操作');
    }

    ctx.runInBackground(async() => {
      const json = await service.book.combineJsonByTaskIds(questionTasks);
      await service.project.base.setOssData(project.appKey, projectId, 'json', json);
      await service.project.base.setOssData(project.appKey, projectId, 'official.json', cleanJsonNodes(json));

      const htmls = await Promise.all(allTasks.map(async(task) => {
        const html: string = await service.task.base.getOssData(task.appKey, task.taskId, 'formatted.html');
        return html;
      }));
      const html = htmls.join('\n');
      await service.project.base.setOssData(project.appKey, projectId, 'html', html);

      /*
       * const docKey = service.project.base.getOssKey(project.appKey, projectId, 'docx');
       * await service.oss.convertHtmlToDocAndUpload(docKey, html, true);
       */

      const zip = new JSZip();
      await Promise.all(allTasks.map(async(task, idx) => {
        const doc: Buffer = await service.task.base.getOssData(task.appKey, task.taskId, 'docx');
        zip.file(`${idx + 1}${task.taskName}.docx`, doc);
      }));
      const buffer = await zip.generateAsync({
        type: 'nodebuffer',
        compression: 'DEFLATE',
      });
      await service.project.base.setOssData(project.appKey, projectId, 'docx.zip', buffer);
    });
    ctx.body = { status: 0 };
  }

  public async getOwnList() {
    const {
      ctx,
      service,
    } = this;
    const {
      key,
      isOutAdmin,
    } = ctx.input;
    const { userId } = ctx.data;
    const { statuses } = service.project.base;
    const page: number = ctx.input.page > 0 ? ctx.input.page : 1;
    const pageSize: number = ctx.input.pageSize > 0 ? ctx.input.pageSize : 10;
    const whereOpt = {
      key,
      status: statuses.reviewing,
      reviewUserId: userId,
    } as any;
    const [count, projects] = await Promise.all(!isOutAdmin ? [
      service.project.base.relatedCount({ where: whereOpt }),
      service.project.base.getRelatedList({
        page,
        pageSize,
        where: whereOpt,
      })
    ] : [
      service.project.base.exactCount({ where: whereOpt }),
      service.project.base.getExactList({
        page,
        pageSize,
        where: whereOpt,
      })
    ]);
    ctx.body = {
      status: 0,
      data: {
        page,
        pageSize,
        count,
        projects,
      },
    };
  }

  public async checkProjectTask() {
    const {
      service,
      ctx,
    } = this;
    const { projectId } = ctx.input;
    const { statuses } = service.project.base;
    const { statuses: taskStatuses } = service.task.base;
    const { types } = service.book;
    const { userId } = ctx.data;
    const [project, books] = await Promise.all([
      service.project.base.getOne({
        where: { id: projectId },
        attributes: ['status', 'appKey', 'reviewUserId'],
      }),
      service.book.getAll({
        where: { projectId },
        attributes: ['id', 'type'],
      })
    ]);
    const projectMeta = await service.project.meta.getMetas({ projectId });
    if (!project) {
      return ctx.body = baseError.dataNotExistError('项目不存在');
    }
    if (project.status !== statuses.reviewing) {
      return ctx.body = baseError.dataNotExistError('项目处于不能操作的状态');
    }
    if (project.reviewUserId !== userId) {
      return ctx.body = baseError.permissionError('没有操作权限');
    }
    const book = books.find((book) => book.type === types.question);
    if (!book) {
      return ctx.body = baseError.dataNotExistError('试题图书不存在');
    }
    let allTasks = await service.task.base.getAll({ where: { bookId: books.map((book) => book.id) } });

    // v1，v2 合并类型
    if (projectMeta.isNewFlow) {
      // 检查的时候，只检查 标注任务 的状态
      allTasks = allTasks.filter((v) => !v.mergedTaskId);
    }

    const questionTasks = allTasks.filter((task) => task.bookId === book.id);
    if (!questionTasks.length) {
      return ctx.body = baseError.dataNotExistError('不存在需要保存的任务');
    }

    const unSelfOperateTask = questionTasks.filter((task) => task.status === taskStatuses.operatAdmin && task.operatAdminUserId !== userId);
    if (unSelfOperateTask.length) {
      const users = await service.user.search(unSelfOperateTask.map((task) => task.operatAdminUserId));
      return ctx.body = baseError.dataAlreadyExistError(unSelfOperateTask.map((task, index) => `${users?.[index]?.nickname || task.operatAdminUserId}正在处理${task.taskName}`).join('\n'));
    }

    const unCompleteTask = questionTasks.filter((task) => task.status !== taskStatuses.operatAdmin && task.status !== taskStatuses.reviewed);
    if (unCompleteTask.length) {
      return ctx.body = baseError.dataAlreadyExistError(unCompleteTask.map((task) => `${task.taskName}未发布`).join('\n'));
    }

    const selfOperateTask = questionTasks.filter((task) => task.status === taskStatuses.operatAdmin && task.operatAdminUserId === userId);
    if (selfOperateTask.length) {
      await service.task.base.update(
        { status: taskStatuses.reviewed },
        { where: { [Op.or]: selfOperateTask.map(({ taskId }) => ({ taskId })) } }
      );
    }

    ctx.body = { status: 0 };
  }

}
